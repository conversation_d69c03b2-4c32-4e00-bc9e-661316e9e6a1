"use strict";
const TABBAR_MAP = {
  CUSTOM_TABBAR_WITH_CACHE: 2
};
const selectedTabbarStrategy = TABBAR_MAP.CUSTOM_TABBAR_WITH_CACHE;
const tabbarList = [
  {
    iconPath: "static/tabbar/home.png",
    selectedIconPath: "static/tabbar/homeHL.png",
    pagePath: "pages/workspace/index",
    text: "工作台",
    icon: "home",
    // 选用 UI 框架自带的 icon 时，iconType 为 uiLib
    iconType: "uiLib"
  },
  {
    iconPath: "static/tabbar/market.png",
    selectedIconPath: "static/tabbar/marketHL.png",
    pagePath: "pages/quotes/my-list",
    text: "报价管理",
    icon: "list",
    iconType: "uiLib"
  },
  {
    iconPath: "static/tabbar/trade.png",
    selectedIconPath: "static/tabbar/tradeHL.png",
    pagePath: "pages/trade/execute",
    text: "交易中心",
    icon: "thumb-up",
    iconType: "uiLib"
  },
  {
    iconPath: "static/tabbar/profile.png",
    selectedIconPath: "static/tabbar/profileHL.png",
    pagePath: "pages/profile/index",
    text: "我的",
    icon: "user",
    iconType: "uiLib"
  }
];
exports.TABBAR_MAP = TABBAR_MAP;
exports.selectedTabbarStrategy = selectedTabbarStrategy;
exports.tabbarList = tabbarList;
