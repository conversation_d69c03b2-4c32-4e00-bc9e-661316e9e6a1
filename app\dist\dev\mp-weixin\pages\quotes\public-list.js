"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_user = require("../../api/user.js");
const api_quotation = require("../../api/quotation.js");
if (!Array) {
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_img2 + _easycom_wd_icon2 + _easycom_wd_tag2 + _easycom_wd_loading2 + _component_layout_default_uni)();
}
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
if (!Math) {
  (_easycom_wd_img + _easycom_wd_icon + _easycom_wd_tag + QuotationCard + _easycom_wd_loading)();
}
const QuotationCard = () => "../../components/marketplace/QuotationCard.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "UserHomePage"
}), {
  __name: "public-list",
  setup(__props) {
    const userId = common_vendor.ref();
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const userProfile = common_vendor.ref();
    const quotationList = common_vendor.ref([]);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const total = common_vendor.ref(0);
    const pageTitle = common_vendor.computed(() => {
      var _a, _b;
      if ((_a = userProfile.value) == null ? void 0 : _a.companyName) {
        return userProfile.value.companyName;
      }
      return ((_b = userProfile.value) == null ? void 0 : _b.nickName) || "用户主页";
    });
    common_vendor.computed(() => {
      return userProfile.value && userProfile.value.enable === 1;
    });
    function loadUserProfile() {
      return __async(this, null, function* () {
        if (!userId.value)
          return;
        try {
          const res = yield api_user.getUserPublicProfile(userId.value);
          userProfile.value = res.data;
          common_vendor.index.setNavigationBarTitle({
            title: pageTitle.value
          });
        } catch (error) {
          console.error("加载用户档案失败:", error);
          common_vendor.index.showToast({
            title: "用户不存在",
            icon: "error"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        }
      });
    }
    function loadQuotationList(refresh = false) {
      return __async(this, null, function* () {
        if (!userId.value)
          return;
        if (refresh) {
          currentPage.value = 1;
          quotationList.value = [];
          isRefreshing.value = true;
        } else {
          isLoading.value = true;
        }
        try {
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value,
            userID: userId.value
          };
          const res = yield api_quotation.getPublicQuotationList(params);
          const { list, total: totalCount } = res.data;
          if (refresh) {
            quotationList.value = list;
          } else {
            quotationList.value.push(...list);
          }
          total.value = totalCount;
          hasMore.value = quotationList.value.length < totalCount;
        } catch (error) {
          console.error("加载报价列表失败:", error);
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "error"
          });
        } finally {
          isLoading.value = false;
          isRefreshing.value = false;
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || isLoading.value)
          return;
        currentPage.value++;
        yield loadQuotationList();
      });
    }
    function onRefresh() {
      return __async(this, null, function* () {
        yield loadQuotationList(true);
      });
    }
    function viewQuotationDetail(quotation) {
      common_vendor.index.navigateTo({
        url: `/pages/quotes/detail?id=${quotation.id}`
      });
    }
    function handlePublisherClick() {
    }
    common_vendor.onLoad((options) => {
      if (options == null ? void 0 : options.id) {
        userId.value = parseInt(options.id);
      } else {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "error"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
        return;
      }
    });
    common_vendor.onMounted(() => __async(this, null, function* () {
      if (userId.value) {
        yield loadUserProfile();
        yield loadQuotationList();
      }
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: userProfile.value
      }, userProfile.value ? common_vendor.e({
        b: common_vendor.p({
          src: userProfile.value.headerImg,
          width: "120rpx",
          height: "120rpx",
          mode: "aspectFill",
          round: true,
          ["custom-class"]: "user-avatar"
        }),
        c: common_vendor.t(userProfile.value.companyName || userProfile.value.nickName),
        d: userProfile.value.companyName
      }, userProfile.value.companyName ? {
        e: common_vendor.t(userProfile.value.nickName)
      } : {}, {
        f: userProfile.value.companyAddress
      }, userProfile.value.companyAddress ? {
        g: common_vendor.p({
          name: "location",
          size: "24rpx"
        }),
        h: common_vendor.t(userProfile.value.companyAddress)
      } : {}, {
        i: common_vendor.t(userProfile.value.enable === 1 ? "正常" : "冻结"),
        j: common_vendor.p({
          type: userProfile.value.enable === 1 ? "success" : "danger",
          size: "small"
        })
      }) : {}, {
        k: common_vendor.t(total.value),
        l: quotationList.value.length > 0
      }, quotationList.value.length > 0 ? {
        m: common_vendor.f(quotationList.value, (quotation, k0, i0) => {
          return {
            a: quotation.id,
            b: common_vendor.o(viewQuotationDetail, quotation.id),
            c: common_vendor.o(handlePublisherClick, quotation.id),
            d: "3ff80c61-4-" + i0 + ",3ff80c61-0",
            e: common_vendor.p({
              quotation
            })
          };
        })
      } : !isLoading.value ? {
        o: common_vendor.p({
          src: "/static/images/empty-quotes.png",
          width: "200rpx",
          height: "200rpx",
          mode: "aspectFit",
          ["custom-class"]: "empty-image"
        })
      } : {}, {
        n: !isLoading.value,
        p: isLoading.value && quotationList.value.length > 0
      }, isLoading.value && quotationList.value.length > 0 ? {
        q: common_vendor.p({
          size: "24rpx",
          ["custom-class"]: "loading-spinner"
        })
      } : {}, {
        r: !hasMore.value && quotationList.value.length > 0
      }, !hasMore.value && quotationList.value.length > 0 ? {} : {}, {
        s: isRefreshing.value,
        t: common_vendor.o(onRefresh),
        v: common_vendor.o(loadMore)
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3ff80c61"]]);
wx.createPage(MiniProgramPage);
