"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_traderequest = require("../../api/traderequest.js");
const utils_toast = require("../../utils/toast.js");
if (!Array) {
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_textarea2 = common_vendor.resolveComponent("wd-textarea");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_tab2 + _easycom_wd_tabs2 + _easycom_wd_input2 + _easycom_wd_textarea2 + _easycom_wd_button2 + _easycom_wd_popup2 + _component_layout_default_uni)();
}
const _easycom_wd_tab = () => "../../node-modules/wot-design-uni/components/wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../../node-modules/wot-design-uni/components/wd-tabs/wd-tabs.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_textarea = () => "../../node-modules/wot-design-uni/components/wd-textarea/wd-textarea.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_tab + _easycom_wd_tabs + TradeRequestList + _easycom_wd_input + _easycom_wd_textarea + _easycom_wd_button + _easycom_wd_popup)();
}
const TradeRequestList = () => "../../components/TradeRequestList.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "SetterManagementPage"
}), {
  __name: "setter-management",
  setup(__props) {
    const tradeRequests = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const refreshing = common_vendor.ref(false);
    const loadingMore = common_vendor.ref(false);
    const statusFilter = common_vendor.ref("");
    const activeTab = common_vendor.ref("");
    const page = common_vendor.ref(1);
    const pageSize = common_vendor.ref(50);
    const hasMore = common_vendor.ref(true);
    const feedbackDialogVisible = common_vendor.ref(false);
    const currentRequest = common_vendor.ref(null);
    const feedbackForm = common_vendor.ref({
      quantity: 0,
      price: 0,
      notes: ""
    });
    const rejectForm = common_vendor.ref({
      reason: ""
    });
    const currentAction = common_vendor.ref("fill");
    const statusTabs = common_vendor.ref([
      { label: "全部", value: "" },
      { label: "执行中", value: "Executing" },
      { label: "已完成", value: "Completed" },
      { label: "已拒绝", value: "Rejected" },
      { label: "已取消", value: "Cancelled" },
      { label: "已过期", value: "Expired" }
    ]);
    const loadMoreText = common_vendor.computed(() => {
      if (loadingMore.value) {
        return "加载中...";
      }
      if (!hasMore.value) {
        return "没有更多数据了";
      }
      return "上拉加载更多";
    });
    const currentMode = common_vendor.computed(() => {
      return "setter";
    });
    const pageTitle = common_vendor.computed(() => {
      return "交易请求管理";
    });
    function loadTradeRequests(isRefresh = false) {
      return __async(this, null, function* () {
        if ((loading.value || loadingMore.value) && !isRefresh)
          return;
        if (isRefresh) {
          refreshing.value = true;
          page.value = 1;
          hasMore.value = true;
        } else if (page.value === 1) {
          loading.value = true;
        } else {
          loadingMore.value = true;
        }
        try {
          const currentPage = isRefresh ? 1 : page.value;
          const response = yield api_traderequest.getMyTradeRequestsAsSetter({
            status: statusFilter.value || void 0,
            page: currentPage,
            pageSize: pageSize.value
          });
          if (response.code === 0) {
            const newRequests = response.data.list;
            if (isRefresh || page.value === 1) {
              tradeRequests.value = newRequests;
              if (isRefresh) {
                utils_toast.toast.success("刷新成功");
              }
            } else {
              tradeRequests.value.push(...newRequests);
            }
            hasMore.value = newRequests.length === pageSize.value;
          } else {
            utils_toast.toast.error(response.msg || "获取交易请求失败");
          }
        } catch (error) {
          console.error("获取交易请求失败:", error);
          utils_toast.toast.error("网络错误");
        } finally {
          if (isRefresh) {
            console.log("停止下拉刷新");
            refreshing.value = false;
            common_vendor.nextTick$1(() => {
              common_vendor.index.stopPullDownRefresh();
              console.log("uni.stopPullDownRefresh() 已调用");
            });
          } else if (page.value === 1) {
            loading.value = false;
          } else {
            loadingMore.value = false;
          }
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || loadingMore.value || loading.value)
          return;
        page.value++;
        yield loadTradeRequests();
      });
    }
    function handleTabChange(changeEvent) {
      console.log("Tab切换:", { changeEvent, oldActiveTab: activeTab.value });
      const newValue = changeEvent.name;
      activeTab.value = newValue;
      statusFilter.value = newValue;
      console.log("更新后:", { activeTab: activeTab.value, statusFilter: statusFilter.value });
      page.value = 1;
      hasMore.value = true;
      loadTradeRequests();
    }
    function handleRefresh() {
      loadTradeRequests(true);
    }
    function handleConvertToSimulation(request) {
      utils_toast.toast.warning("转模拟功能正在开发中");
      console.log("转模拟:", request);
    }
    function handleConvertToTrade(request) {
      utils_toast.toast.warning("转交易功能正在开发中");
      console.log("转交易:", request);
    }
    function handleRequestAction(request, action) {
      currentRequest.value = request;
      currentAction.value = action;
      if (action === "fill") {
        feedbackForm.value = {
          quantity: request.requestedQuantity - request.executedQuantity,
          price: request.requestedPrice || 0,
          notes: ""
        };
      } else {
        rejectForm.value = {
          reason: ""
        };
      }
      feedbackDialogVisible.value = true;
    }
    function handleFill(request) {
      handleRequestAction(request, "fill");
    }
    function handleReject(request) {
      handleRequestAction(request, "reject");
    }
    function submitFeedback() {
      return __async(this, null, function* () {
        var _a;
        if (!currentRequest.value)
          return;
        loading.value = true;
        try {
          if (currentAction.value === "fill") {
            if (!feedbackForm.value.quantity || feedbackForm.value.quantity <= 0) {
              utils_toast.toast.error("请输入有效的成交数量");
              return;
            }
            if (!feedbackForm.value.price || feedbackForm.value.price <= 0) {
              utils_toast.toast.error("请输入有效的成交价格");
              return;
            }
            const remainingQuantity = currentRequest.value.requestedQuantity - currentRequest.value.executedQuantity;
            if (feedbackForm.value.quantity > remainingQuantity) {
              utils_toast.toast.error(`成交数量不能超过剩余数量 ${remainingQuantity}`);
              return;
            }
            const response = yield api_traderequest.manualFeedback(currentRequest.value.ID, {
              quantity: Number(feedbackForm.value.quantity),
              price: Number(feedbackForm.value.price),
              notes: feedbackForm.value.notes
            });
            if (response.code === 0) {
              utils_toast.toast.success("成交处理成功");
              feedbackDialogVisible.value = false;
              loadTradeRequests(true);
            } else {
              utils_toast.toast.error(response.msg || "成交失败");
            }
          } else {
            if (!((_a = rejectForm.value.reason) == null ? void 0 : _a.trim())) {
              utils_toast.toast.error("拒绝时请填写原因");
              return;
            }
            const response = yield api_traderequest.rejectTradeRequest(currentRequest.value.ID, {
              reason: rejectForm.value.reason
            });
            if (response.code === 0) {
              utils_toast.toast.success("拒绝处理成功");
              feedbackDialogVisible.value = false;
              loadTradeRequests(true);
            } else {
              utils_toast.toast.error(response.msg || "拒绝失败");
            }
          }
        } catch (error) {
          console.error("提交反馈失败:", error);
          utils_toast.toast.error("操作失败，请重试");
        } finally {
          loading.value = false;
        }
      });
    }
    common_vendor.onLoad(() => {
      common_vendor.index.setNavigationBarTitle({
        title: pageTitle.value
      });
    });
    common_vendor.onMounted(() => {
      activeTab.value = statusFilter.value;
      loadTradeRequests();
    });
    common_vendor.onPullDownRefresh(() => __async(this, null, function* () {
      console.log("下拉刷新开始");
      try {
        yield loadTradeRequests(true);
        console.log("下拉刷新完成");
      } catch (error) {
        console.error("下拉刷新失败:", error);
        common_vendor.index.stopPullDownRefresh();
      }
      setTimeout(() => {
        if (refreshing.value) {
          console.log("下拉刷新超时，强制停止");
          refreshing.value = false;
          common_vendor.index.stopPullDownRefresh();
        }
      }, 5e3);
    }));
    common_vendor.onReachBottom(() => {
      loadMore();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(statusTabs.value, (tab, k0, i0) => {
          return {
            a: tab.value,
            b: "fe014edc-2-" + i0 + ",fe014edc-1",
            c: common_vendor.p({
              title: tab.label,
              name: tab.value
            })
          };
        }),
        b: common_vendor.o(handleTabChange),
        c: common_vendor.o(($event) => activeTab.value = $event),
        d: common_vendor.p({
          modelValue: activeTab.value
        }),
        e: common_vendor.o(handleFill),
        f: common_vendor.o(handleReject),
        g: common_vendor.o(handleConvertToSimulation),
        h: common_vendor.o(handleConvertToTrade),
        i: common_vendor.o(handleRefresh),
        j: common_vendor.p({
          requests: tradeRequests.value,
          loading: loading.value,
          refreshing: refreshing.value,
          mode: currentMode.value
        }),
        k: tradeRequests.value.length > 0
      }, tradeRequests.value.length > 0 ? common_vendor.e({
        l: loadingMore.value
      }, loadingMore.value ? {
        m: common_vendor.t(loadMoreText.value)
      } : {
        n: common_vendor.t(loadMoreText.value)
      }) : {}, {
        o: common_vendor.t(currentAction.value === "fill" ? "确认成交" : "确认拒绝"),
        p: currentRequest.value
      }, currentRequest.value ? common_vendor.e({
        q: common_vendor.t(currentRequest.value.requestType === "PointPrice" ? "点价" : "洗基差"),
        r: common_vendor.t(currentRequest.value.requestedQuantity),
        s: currentRequest.value.requestedPrice
      }, currentRequest.value.requestedPrice ? {
        t: common_vendor.t(currentRequest.value.requestedPrice)
      } : {}) : {}, {
        v: currentAction.value === "fill"
      }, currentAction.value === "fill" ? {
        w: common_vendor.o(($event) => feedbackForm.value.quantity = $event),
        x: common_vendor.p({
          label: "成交数量",
          type: "number",
          placeholder: "请输入成交数量",
          max: currentRequest.value ? currentRequest.value.requestedQuantity - currentRequest.value.executedQuantity : void 0,
          modelValue: feedbackForm.value.quantity
        }),
        y: common_vendor.o(($event) => feedbackForm.value.price = $event),
        z: common_vendor.p({
          label: "成交价格",
          type: "number",
          placeholder: "请输入成交价格",
          modelValue: feedbackForm.value.price
        }),
        A: common_vendor.o(($event) => feedbackForm.value.notes = $event),
        B: common_vendor.p({
          label: "备注",
          placeholder: "成交备注（可选）",
          modelValue: feedbackForm.value.notes
        })
      } : {
        C: common_vendor.o(($event) => rejectForm.value.reason = $event),
        D: common_vendor.p({
          label: "拒绝原因",
          placeholder: "请填写拒绝原因",
          required: true,
          modelValue: rejectForm.value.reason
        })
      }, {
        E: common_vendor.o(($event) => feedbackDialogVisible.value = false),
        F: common_vendor.p({
          type: "info"
        }),
        G: common_vendor.t(currentAction.value === "fill" ? "成交" : "拒绝"),
        H: common_vendor.o(submitFeedback),
        I: common_vendor.p({
          type: currentAction.value === "fill" ? "success" : "error"
        }),
        J: common_vendor.o(($event) => feedbackDialogVisible.value = $event),
        K: common_vendor.p({
          position: "center",
          ["close-on-click-modal"]: false,
          modelValue: feedbackDialogVisible.value
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fe014edc"]]);
wx.createPage(MiniProgramPage);
