<layout-default-uni class="data-v-e9958c03" u-s="{{['d']}}" u-i="e9958c03-0" bind:__l="__l"><view class="demo-page data-v-e9958c03"><view class="demo-header data-v-e9958c03"><text class="page-title data-v-e9958c03">期货合约选择器</text><text class="page-desc data-v-e9958c03">演示期货合约选择器组件的各种使用方式</text></view><view class="demo-content data-v-e9958c03"><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">基础用法</text><text class="section-desc data-v-e9958c03">三级联动选择：交易所 → 商品 → 合约</text></view><view class="form-item data-v-e9958c03"><instrument-selector wx:if="{{c}}" class="data-v-e9958c03" bindchange="{{a}}" u-i="e9958c03-1,e9958c03-0" bind:__l="__l" bindupdateModelValue="{{b}}" u-p="{{c}}"/></view><view wx:if="{{d}}" class="selected-info data-v-e9958c03"><text class="info-title data-v-e9958c03">✅ 已选择合约:</text><view class="info-content data-v-e9958c03"><view class="info-row data-v-e9958c03"><text class="info-label data-v-e9958c03">显示名称:</text><text class="info-value highlight data-v-e9958c03">{{e}}</text></view><view class="info-row data-v-e9958c03"><text class="info-label data-v-e9958c03">合约代码:</text><text class="info-value data-v-e9958c03">{{f}}</text></view><view class="info-row data-v-e9958c03"><text class="info-label data-v-e9958c03">合约名称:</text><text class="info-value data-v-e9958c03">{{g}}</text></view><view class="info-row data-v-e9958c03"><text class="info-label data-v-e9958c03">商品名称:</text><text class="info-value data-v-e9958c03">{{h}}</text></view><view class="info-row data-v-e9958c03"><text class="info-label data-v-e9958c03">交易所:</text><text class="info-value data-v-e9958c03">{{i}} ({{j}})</text></view></view></view></view><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">自定义占位符</text><text class="section-desc data-v-e9958c03">自定义提示文字</text></view><view class="form-item data-v-e9958c03"><instrument-selector wx:if="{{m}}" class="data-v-e9958c03" bindchange="{{k}}" u-i="e9958c03-2,e9958c03-0" bind:__l="__l" bindupdateModelValue="{{l}}" u-p="{{m}}"/></view></view><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">禁用状态</text><text class="section-desc data-v-e9958c03">禁用选择器的交互</text></view><view class="form-item data-v-e9958c03"><instrument-selector wx:if="{{o}}" class="data-v-e9958c03" u-i="e9958c03-3,e9958c03-0" bind:__l="__l" bindupdateModelValue="{{n}}" u-p="{{o}}"/></view></view><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">不可清空</text><text class="section-desc data-v-e9958c03">隐藏清空按钮，选择后不可清空</text></view><view class="form-item data-v-e9958c03"><instrument-selector wx:if="{{r}}" class="data-v-e9958c03" bindchange="{{p}}" u-i="e9958c03-4,e9958c03-0" bind:__l="__l" bindupdateModelValue="{{q}}" u-p="{{r}}"/></view></view><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">清空功能演示</text><text class="section-desc data-v-e9958c03">演示清空按钮的使用，可清空状态下会显示清空按钮</text></view><view class="form-item data-v-e9958c03"><instrument-selector wx:if="{{v}}" class="data-v-e9958c03" bindchange="{{s}}" u-i="e9958c03-5,e9958c03-0" bind:__l="__l" bindupdateModelValue="{{t}}" u-p="{{v}}"/></view><view wx:if="{{w}}" class="clear-demo-info data-v-e9958c03"><text class="clear-info-text data-v-e9958c03">✨ 已选择合约，现在可以点击清空按钮清除选择</text></view></view><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">操作演示</text><text class="section-desc data-v-e9958c03">演示常用操作功能</text></view><view class="button-group data-v-e9958c03"><button class="demo-button primary data-v-e9958c03" bindtap="{{x}}"> 提交选择 </button><button class="demo-button secondary data-v-e9958c03" bindtap="{{y}}"> 重置所有选择 </button></view></view><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">🚀 组件特性</text></view><view class="feature-list data-v-e9958c03"><view class="feature-item data-v-e9958c03"><text class="feature-icon data-v-e9958c03">🔄</text><text class="feature-text data-v-e9958c03">三级联动选择：交易所 → 商品 → 合约</text></view><view class="feature-item data-v-e9958c03"><text class="feature-icon data-v-e9958c03">⚡</text><text class="feature-text data-v-e9958c03">异步数据加载，支持大量合约数据</text></view><view class="feature-item data-v-e9958c03"><text class="feature-icon data-v-e9958c03">📱</text><text class="feature-text data-v-e9958c03">完美适配移动端触摸操作</text></view><view class="feature-item data-v-e9958c03"><text class="feature-icon data-v-e9958c03">🎨</text><text class="feature-text data-v-e9958c03">自定义样式，支持禁用和清空控制</text></view><view class="feature-item data-v-e9958c03"><text class="feature-icon data-v-e9958c03">🔍</text><text class="feature-text data-v-e9958c03">智能筛选，快速定位目标合约</text></view><view class="feature-item data-v-e9958c03"><text class="feature-icon data-v-e9958c03">💾</text><text class="feature-text data-v-e9958c03">数据缓存，避免重复请求</text></view></view></view><view class="demo-section data-v-e9958c03"><view class="section-header data-v-e9958c03"><text class="section-title data-v-e9958c03">📖 使用方法</text></view><view class="usage-code data-v-e9958c03"><text class="code-title data-v-e9958c03">基础用法:</text><view class="code-block data-v-e9958c03"><text class="code-line data-v-e9958c03"><InstrumentSelector</text><text class="code-line data-v-e9958c03"> v-model="selectedInstrument"</text><text class="code-line data-v-e9958c03"> label="选择期货合约:"</text><text class="code-line data-v-e9958c03"> placeholder="请选择期货合约"</text><text class="code-line data-v-e9958c03"> @change="handleChange"</text><text class="code-line data-v-e9958c03">/></text></view><text class="code-title data-v-e9958c03">Props配置:</text><view class="props-list data-v-e9958c03"><view class="prop-item data-v-e9958c03"><text class="prop-name data-v-e9958c03">v-model</text><text class="prop-desc data-v-e9958c03">绑定选中的合约对象</text></view><view class="prop-item data-v-e9958c03"><text class="prop-name data-v-e9958c03">placeholder</text><text class="prop-desc data-v-e9958c03">占位符文字</text></view><view class="prop-item data-v-e9958c03"><text class="prop-name data-v-e9958c03">disabled</text><text class="prop-desc data-v-e9958c03">是否禁用</text></view><view class="prop-item data-v-e9958c03"><text class="prop-name data-v-e9958c03">label</text><text class="prop-desc data-v-e9958c03">选择器标签文字</text></view><view class="prop-item data-v-e9958c03"><text class="prop-name data-v-e9958c03">clearable</text><text class="prop-desc data-v-e9958c03">是否可清空</text></view></view></view></view></view></view></layout-default-uni>