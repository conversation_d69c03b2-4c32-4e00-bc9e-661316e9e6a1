"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const types_instrument = require("../types/instrument.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
if (!Math) {
  InstrumentSelector();
}
const InstrumentSelector = () => "../components/InstrumentSelector.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "InstrumentSelectorDemo"
}), {
  __name: "instrument",
  setup(__props) {
    const selectedInstrument = common_vendor.ref(null);
    const selectedInstrument2 = common_vendor.ref(null);
    const selectedInstrument3 = common_vendor.ref(null);
    const selectedInstrument4 = common_vendor.ref(null);
    const selectionSummary = common_vendor.computed(() => {
      if (!selectedInstrument.value)
        return null;
      return __spreadProps(__spreadValues({}, selectedInstrument.value), {
        exchangeName: types_instrument.ExchangeMap[selectedInstrument.value.exchange_id] || selectedInstrument.value.exchange_id,
        displayName: `${types_instrument.ExchangeMap[selectedInstrument.value.exchange_id] || selectedInstrument.value.exchange_id}.${selectedInstrument.value.instrument_id}`
      });
    });
    const handleInstrumentChange = (value, type = "基础") => {
      console.log(`${type}选择器变化:`, value);
      common_vendor.index.showToast({
        title: value ? `选择了 ${value.instrument_id}` : "已清空选择",
        icon: value ? "success" : "none",
        duration: 1500
      });
    };
    const resetAllSelections = () => {
      selectedInstrument.value = null;
      selectedInstrument2.value = null;
      selectedInstrument3.value = null;
      selectedInstrument4.value = null;
      common_vendor.index.showToast({
        title: "已重置所有选择",
        icon: "success"
      });
    };
    const handleSubmit = () => {
      if (!selectedInstrument.value) {
        common_vendor.index.showToast({
          title: "请先选择期货合约",
          icon: "error"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "提交确认",
        content: `确认提交合约：${selectedInstrument.value.instrument_id}？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "提交成功！",
              icon: "success"
            });
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o((value) => handleInstrumentChange(value, "基础")),
        b: common_vendor.o(($event) => selectedInstrument.value = $event),
        c: common_vendor.p({
          label: "选择期货合约:",
          placeholder: "请选择期货合约",
          modelValue: selectedInstrument.value
        }),
        d: selectionSummary.value
      }, selectionSummary.value ? {
        e: common_vendor.t(selectionSummary.value.displayName),
        f: common_vendor.t(selectionSummary.value.instrument_id),
        g: common_vendor.t(selectionSummary.value.instrument_name),
        h: common_vendor.t(selectionSummary.value.product_name),
        i: common_vendor.t(selectionSummary.value.exchangeName),
        j: common_vendor.t(selectionSummary.value.exchange_id)
      } : {}, {
        k: common_vendor.o((value) => handleInstrumentChange(value, "主力合约")),
        l: common_vendor.o(($event) => selectedInstrument2.value = $event),
        m: common_vendor.p({
          label: "选择主力合约:",
          placeholder: "选择一个主力合约进行交易",
          modelValue: selectedInstrument2.value
        }),
        n: common_vendor.o(($event) => selectedInstrument3.value = $event),
        o: common_vendor.p({
          label: "禁用状态:",
          placeholder: "此选择器已被禁用",
          disabled: true,
          modelValue: selectedInstrument3.value
        }),
        p: common_vendor.o((value) => handleInstrumentChange(value, "不可清空")),
        q: common_vendor.o(($event) => selectedInstrument4.value = $event),
        r: common_vendor.p({
          label: "选择后不可清空:",
          placeholder: "选择后无法清空",
          clearable: false,
          modelValue: selectedInstrument4.value
        }),
        s: common_vendor.o((value) => handleInstrumentChange(value, "可清空")),
        t: common_vendor.o(($event) => selectedInstrument.value = $event),
        v: common_vendor.p({
          label: "可清空的选择器:",
          placeholder: "选择合约后会显示清空按钮",
          clearable: true,
          modelValue: selectedInstrument.value
        }),
        w: selectedInstrument.value
      }, selectedInstrument.value ? {} : {}, {
        x: common_vendor.o(handleSubmit),
        y: common_vendor.o(resetAllSelections)
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e9958c03"]]);
wx.createPage(MiniProgramPage);
