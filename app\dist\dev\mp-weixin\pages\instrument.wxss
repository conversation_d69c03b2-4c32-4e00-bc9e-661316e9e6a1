/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.demo-page.data-v-e9958c03 {
  min-height: 100vh;
  background: #f8f9fa;
}
.demo-header.data-v-e9958c03 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80rpx 32rpx 60rpx;
  color: white;
  text-align: center;
}
.page-title.data-v-e9958c03 {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}
.page-desc.data-v-e9958c03 {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}
.demo-content.data-v-e9958c03 {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 48rpx;
}
.demo-section.data-v-e9958c03 {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.section-header.data-v-e9958c03 {
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.section-title.data-v-e9958c03 {
  font-size: 32rpx;
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 8rpx;
}
.section-desc.data-v-e9958c03 {
  font-size: 26rpx;
  color: #606266;
  display: block;
}
.form-item.data-v-e9958c03 {
  margin-bottom: 32rpx;
}
.form-label.data-v-e9958c03 {
  font-size: 28rpx;
  color: #606266;
  display: block;
  margin-bottom: 16rpx;
}
.selected-info.data-v-e9958c03 {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12rpx;
  border: 1rpx solid #0ea5e9;
}
.info-title.data-v-e9958c03 {
  font-size: 28rpx;
  font-weight: 600;
  color: #0369a1;
  display: block;
  margin-bottom: 16rpx;
}
.info-content.data-v-e9958c03 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.info-row.data-v-e9958c03 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
}
.info-label.data-v-e9958c03 {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
  min-width: 160rpx;
}
.info-value.data-v-e9958c03 {
  font-size: 26rpx;
  color: #1e293b;
  flex: 1;
  text-align: right;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
}
.info-value.highlight.data-v-e9958c03 {
  color: #0369a1;
  font-weight: 600;
  font-size: 28rpx;
}
.feature-list.data-v-e9958c03 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.feature-item.data-v-e9958c03 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.feature-icon.data-v-e9958c03 {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}
.feature-text.data-v-e9958c03 {
  font-size: 28rpx;
  color: #606266;
  flex: 1;
}
.clear-demo-info.data-v-e9958c03 {
  margin-top: 24rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-radius: 12rpx;
  border: 1rpx solid #22c55e;
}
.clear-info-text.data-v-e9958c03 {
  font-size: 26rpx;
  color: #15803d;
  font-weight: 500;
  display: block;
  line-height: 1.5;
}
.button-group.data-v-e9958c03 {
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}
.demo-button.data-v-e9958c03 {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}
.demo-button.primary.data-v-e9958c03 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.demo-button.primary.data-v-e9958c03:active {
  transform: scale(0.98);
  opacity: 0.9;
}
.demo-button.secondary.data-v-e9958c03 {
  background: #f8f9fa;
  color: #606266;
  border: 1rpx solid #e4e7ed;
}
.demo-button.secondary.data-v-e9958c03:active {
  background: #e9ecef;
  transform: scale(0.98);
}
.usage-code.data-v-e9958c03 {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1rpx solid #e4e7ed;
}
.code-title.data-v-e9958c03 {
  font-size: 28rpx;
  font-weight: 600;
  color: #303133;
  display: block;
  margin-bottom: 16rpx;
}
.code-block.data-v-e9958c03 {
  background: #2d3748;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 24rpx;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
}
.code-line.data-v-e9958c03 {
  display: block;
  color: #e2e8f0;
  font-size: 24rpx;
  line-height: 1.6;
  margin-bottom: 4rpx;
}
.code-line.data-v-e9958c03:last-child {
  margin-bottom: 0;
}
.props-list.data-v-e9958c03 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.prop-item.data-v-e9958c03 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 12rpx;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e4e7ed;
}
.prop-name.data-v-e9958c03 {
  font-size: 24rpx;
  color: #667eea;
  font-weight: 600;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
  min-width: 140rpx;
  background: #f0f4ff;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}
.prop-desc.data-v-e9958c03 {
  font-size: 24rpx;
  color: #606266;
  flex: 1;
  line-height: 1.5;
}