/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-3ff80c61 {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  box-sizing: border-box;
}
.user-profile-header.data-v-3ff80c61 {
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.user-profile-header .profile-card.data-v-3ff80c61 {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  gap: 20rpx;
  animation: slideInDown-3ff80c61 0.3s ease-out;
}
.user-profile-header .profile-card .avatar-section.data-v-3ff80c61 {
  flex-shrink: 0;
}
.user-profile-header .profile-card .avatar-section.data-v-3ff80c61 .user-avatar {
  border: 4rpx solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.user-profile-header .profile-card .user-info.data-v-3ff80c61 {
  flex: 1;
  min-width: 0;
}
.user-profile-header .profile-card .user-info .company-name.data-v-3ff80c61 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.user-profile-header .profile-card .user-info .nick-name.data-v-3ff80c61 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.user-profile-header .profile-card .user-info .company-address.data-v-3ff80c61 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.user-profile-header .profile-card .status-indicator.data-v-3ff80c61 {
  flex-shrink: 0;
}
.section-header.data-v-3ff80c61 {
  padding: 0 20rpx 20rpx;
}
.section-header .section-title-row.data-v-3ff80c61 {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
}
.section-header .section-title.data-v-3ff80c61 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.section-header .section-subtitle.data-v-3ff80c61 {
  font-size: 24rpx;
  color: #666;
}
.scroll-container.data-v-3ff80c61 {
  flex: 1;
  padding: 0 20rpx;
  box-sizing: border-box;
}
.quotation-list.data-v-3ff80c61 {
  width: 100%;
  box-sizing: border-box;
}
.quotation-list.data-v-3ff80c61 .quotation-card {
  background: rgba(255, 255, 255, 0.95) !important;
  -webkit-backdrop-filter: blur(10rpx) !important;
          backdrop-filter: blur(10rpx) !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12) !important;
  transition: all 0.3s ease !important;
}
.quotation-list.data-v-3ff80c61 .quotation-card:hover {
  transform: translateY(-4rpx) !important;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15) !important;
}
.empty-state.data-v-3ff80c61 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  margin: 36rpx 0;
  animation: fadeIn-3ff80c61 0.5s ease-out;
}
.empty-state.data-v-3ff80c61 .empty-image {
  opacity: 0.6;
  filter: grayscale(20%);
}
.empty-state .empty-text.data-v-3ff80c61 {
  font-size: 28rpx;
  color: #606266;
  margin-top: 24rpx;
  text-align: center;
  font-weight: 500;
}
.loading-more.data-v-3ff80c61 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin: 20rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.loading-more.data-v-3ff80c61 .loading-spinner {
  color: #667eea !important;
}
.loading-more .loading-text.data-v-3ff80c61 {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}
.no-more.data-v-3ff80c61 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  margin: 36rpx 0;
}
.no-more .divider.data-v-3ff80c61 {
  flex: 1;
  height: 2rpx;
  background-color: #d0d4d9;
  min-width: 20rpx;
}
.no-more .no-more-text.data-v-3ff80c61 {
  padding: 0 24rpx;
  font-size: 24rpx;
  color: #c0c4cc;
  white-space: nowrap;
}
@keyframes slideInDown-3ff80c61 {
from {
    opacity: 0;
    transform: translateY(-20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes fadeIn-3ff80c61 {
from {
    opacity: 0;
    transform: translateY(10rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@media (max-width: 750rpx) {
.user-profile-header.data-v-3ff80c61 {
    padding: 16rpx;
}
.user-profile-header .profile-card.data-v-3ff80c61 {
    padding: 24rpx;
}
.scroll-container.data-v-3ff80c61 {
    padding: 0 16rpx;
}
.empty-state.data-v-3ff80c61 {
    margin: 16rpx 0;
    padding: 80rpx 20rpx;
}
}