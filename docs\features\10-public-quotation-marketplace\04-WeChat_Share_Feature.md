# 功能设计文档 V4: 微信小程序分享用户主页

> **版本**: 4.0.0
> **负责人**: Gemini
> **状态**: 方案设计

---

## 1. 功能概述 (Functional Overview)

本功能旨在为小程序增加社交分享能力，允许用户在“我的报价”页面 (`@/src/pages/quotes/my-list.vue`) 上，生成一个专属的分享链接，并将其发送给微信好友或微信群。其他用户点击此分享卡片后，将直接跳转到分享者的“用户主页” (`@/src/pages/quotes/public-list.vue`)，查看其公开发布的所有有效报价。 

此举旨在利用微信的社交网络，增强用户报价的曝光度和可发现性，为平台带来更多的潜在交易机会。

---

## 2. 微信官方文档研究与技术选型

根据微信官方小程序开发文档，实现页面分享功能主要有两种方式：

1.  **页面级分享**: 通过在页面的 `<script>` 部分定义 `onShareAppMessage` 生命周期函数。当用户点击小程序右上角的胶囊菜单并选择“发送给朋友”时，会触发此函数。这是最基础的分享形式。
2.  **组件级分享**: 通过在页面上放置一个 `<button>` 组件，并设置其 `open-type="share"` 属性。用户点击该按钮会直接拉起分享面板，提供更主动、更明确的分享入口。

**技术选型**: 
为了提供最佳的用户体验，我们将 **同时采用这两种方式**：
- **页面级分享** 作为基础能力，满足习惯使用右上角菜单的用户。
- **组件级分享** 作为主要交互，在页面上提供一个清晰的“分享我的主页”按钮，引导用户主动分享。

---

## 3. 实施方案与步骤 (Implementation Plan)

### 3.1. 步骤一：修改“我的报价”页面 (`@/app/src/pages/quotes/my-list.vue`)

#### 3.1.1. UI/UX 调整
- 在页面的导航栏（Navigation Bar）右侧，新增一个“分享”图标按钮。这将是触发分享功能的主要入口。

#### 3.1.2. 逻辑实现
- 在页面的 `<script setup>` 部分，引入 `onShareAppMessage` 生命周期函数。
- 此函数需要从用户状态管理（如 Pinia Store）中获取当前登录用户的 `id` 和 `nickName`。
- `onShareAppMessage` 函数将返回一个对象，用于定义分享卡片的具体内容，结构如下：
    - **`title`**: 分享卡片的标题。为了吸引点击，可以设置为动态内容，例如 `"快来看看 ${currentUser.nickName} 的最新报价"`。
    - **`path`**: 分享卡片的目标路径。这是核心部分，必须严格按照格式设置为 `"/pages/quotes/public-list?id=${currentUser.id}"`。
    - **`imageUrl`**: 分享卡片上显示的封面图片。可以设置为一个默认的、代表平台的 Logo 或宣传图，以保证分享出去的卡片美观大方。

### 3.2. 步骤二：验证“用户主页”的兼容性 (`@/app/src/pages/quotes/public-list.vue`)

- **无需修改**: 根据对现有代码的分析，`public-list.vue` 页面已经在 `onLoad` 生命周期中正确处理了 `options.id` 参数的获取和解析逻辑。
- **验证点**: 确保当用户通过分享链接进入时，页面能够正确接收 `id` 参数，并据此加载对应用户的公开信息和报价列表。

---

## 4. 统一测试用例 (Consolidated Test Cases)

| 用例ID | 模块 | 场景描述 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-SH-001`| 分享功能 | 用户A在“我的报价”页点击“分享”按钮 | 成功拉起微信分享面板。 |
| `TC-SH-002`| 分享内容 | 用户A将链接分享给用户B | 用户B收到的分享卡片，标题和封面图均按设计显示正确。 |
| `TC-SH-003`| 链接跳转 | 用户B点击分享卡片 | 成功跳转到小程序内的“用户主页”，页面地址为 `/pages/quotes/public-list`。 |
| `TC-SH-004`| 数据加载 | 用户B进入页面后 | 页面正确加载并显示了用户A的公司信息及其所有公开的、`Active`状态的报价。 |
| `TC-SH-005`| 异常情况 | 分享的 `id` 参数丢失或无效 | “用户主页”应能优雅地处理错误，显示“参数错误”或“用户不存在”的提示，并引导用户返回。 |

---

## 5. 注意事项 (Notes/Caveats)

- **用户状态**: 必须确保在 `onShareAppMessage` 函数中能稳定、可靠地获取到当前登录用户的ID，否则无法生成正确的分享路径。
- **分享封面**: 需要设计并提供一张默认的分享封面图 (`imageUrl`)，并放置在小程序的 `static` 目录下，以提升分享链接的视觉吸引力。
- **小程序发布**: 微信小程序的分享功能需要在真机上进行测试和验证，开发工具的模拟器可能无法完全模拟真实的分享流程。
