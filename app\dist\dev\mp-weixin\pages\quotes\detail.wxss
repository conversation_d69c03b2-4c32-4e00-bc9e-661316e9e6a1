/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.loading-container.data-v-af301bab {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
}
.loading-container .loading-text.data-v-af301bab {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #909399;
}
.loading-container.data-v-af301bab .loading-spinner {
  --loading-color: #667eea;
}
.detail-container.data-v-af301bab {
  padding-bottom: 200rpx;
}
.card-style.data-v-af301bab {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  overflow: hidden;
}
.publisher-info-section .card-title.data-v-af301bab {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 24rpx;
  color: #333;
}
.publisher-info-section.data-v-af301bab .wd-cell-group {
  margin: 0 -40rpx -40rpx;
}
.main-info-section.data-v-af301bab {
  position: relative;
}
.main-info-section .status-tag.data-v-af301bab {
  position: absolute;
  top: 0;
  right: 0;
  padding: 12rpx 24rpx;
  border-radius: 0 20rpx 0 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: white;
}
.main-info-section .status-tag.active.data-v-af301bab {
  background: linear-gradient(135deg, #4cd964 0%, #667eea 100%);
}
.main-info-section .status-tag.draft.data-v-af301bab {
  background: linear-gradient(135deg, #909399 0%, #606266 100%);
}
.main-info-section .title-price-section.data-v-af301bab {
  margin-bottom: 32rpx;
  padding-right: 140rpx;
}
.main-info-section .title-price-section .quotation-title.data-v-af301bab {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
}
.main-info-section .title-price-section .quotation-price.data-v-af301bab {
  font-size: 52rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  color: transparent;
}
.main-info-section .core-info-card.data-v-af301bab {
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12rpx;
  padding: 24rpx;
}
.main-info-section .core-info-card .info-row.data-v-af301bab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.main-info-section .core-info-card .info-row.data-v-af301bab:last-child {
  margin-bottom: 0;
}
.main-info-section .core-info-card .info-row .label.data-v-af301bab {
  font-size: 28rpx;
  color: #606266;
}
.main-info-section .core-info-card .info-row .value.highlight.data-v-af301bab {
  font-size: 30rpx;
  color: #667eea;
  font-weight: 600;
}
.secondary-info-section.data-v-af301bab .wd-cell-group, .detail-info-section.data-v-af301bab .wd-cell-group {
  margin: -40rpx;
}
.phone-cell.data-v-af301bab {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.phone-cell .phone-number.data-v-af301bab {
  color: #667eea;
  font-weight: 500;
  margin-right: 8rpx;
}
.phone-cell .phone-icon.data-v-af301bab {
  color: #667eea;
  font-size: 32rpx;
}
.multi-line-text.data-v-af301bab {
  max-width: 400rpx;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
  color: #606266;
}
.remaining-time.data-v-af301bab {
  font-weight: 500;
  color: #4cd964;
}
.remaining-time.expired.data-v-af301bab {
  color: #dd524d;
}
.action-section.data-v-af301bab {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.85);
  padding: 24rpx 36rpx;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.action-section .owner-actions.data-v-af301bab, .action-section .public-actions.data-v-af301bab {
  display: flex;
  gap: 24rpx;
  flex-wrap: wrap;
}
.action-section.data-v-af301bab .action-button {
  flex: 1;
  border-radius: 44rpx;
  height: 88rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: none;
  color: #333;
  background: #fff;
  min-width: calc(33.33% - 16rpx);
}
.action-section.data-v-af301bab .action-button:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
}
.action-section.data-v-af301bab .primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.action-section.data-v-af301bab .warning-button {
  background: #f5a623;
  color: white;
}
.action-section.data-v-af301bab .error-button {
  background: #dd524d;
  color: white;
}
.action-section.data-v-af301bab .success-button {
  background: #4cd964;
  color: white;
}
.action-section.data-v-af301bab .success-button.is-plain {
  background: white;
  color: #4cd964;
  border: 1rpx solid #4cd964;
}
.error-container.data-v-af301bab {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  padding: 40rpx;
}
.error-container .error-text.data-v-af301bab {
  font-size: 28rpx;
  color: #909399;
  margin: 24rpx 0 40rpx;
  text-align: center;
}